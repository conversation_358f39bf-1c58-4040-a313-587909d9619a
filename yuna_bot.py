# yuna_bot.py
import os
import logging
import discord
from discord.ext import commands
from dotenv import load_dotenv
import database
import ai_manager

# Load environment variables and logging
load_dotenv()
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)

DISCORD_TOKEN = os.getenv("DISCORD_TOKEN")

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
bot = commands.Bot(command_prefix="/", intents=intents)


@bot.event
async def on_ready():
    """Event that fires when the bot is connected and ready."""
    log.info(f"{bot.user} has connected to Discord!")
    log.info("Initializing database...")
    database.initialize_database()
    log.info("Syncing slash commands...")
    await bot.tree.sync()
    log.info("<PERSON><PERSON> is ready.")


# --- Helper function for context gathering ---
async def gather_context(message: discord.Message, bot: commands.Bot):
    """Analyzes a message and returns a formatted conversation history."""
    history = []

    # Helper to create the author details dictionary
    def get_author_details(author: discord.Member | discord.User):
        return {
            "username": author.name,
            "display_name": author.display_name,
            "server_nickname": author.nick or author.display_name,
        }

    # Case C: AI Channel
    if database.is_ai_channel(message.channel.id):
        log.info(f"Processing message in AI channel: {message.channel.name}")
        # Fetch last 15 messages, reverse them for chronological order
        channel_messages = [msg async for msg in message.channel.history(limit=15)]
        channel_messages.reverse()
        for msg in channel_messages:
            role = "assistant" if msg.author == bot.user else "user"
            if msg.content:
                entry = {"role": role, "content": msg.content}
                if role == "user":
                    entry["author_details"] = get_author_details(msg.author)
                history.append(entry)
        log.info(f"AI Channel: Gathered {len(history)} messages for context")
        return history

    # Determine if the bot should respond (mention or reply)
    is_mention = bot.user in message.mentions
    is_reply_to_bot = (
        message.reference
        and message.reference.resolved
        and message.reference.resolved.author == bot.user
    )

    log.debug(f"Mention check: {is_mention}, Reply check: {is_reply_to_bot}")
    log.debug(f"Message mentions: {[user.name for user in message.mentions]}")
    if message.reference:
        log.debug(
            f"Message is a reply to: {message.reference.resolved.author.name if message.reference.resolved else 'Unknown'}"
        )

    if not (is_mention or is_reply_to_bot):
        log.debug("Bot will not respond - no mention or reply detected")
        return None  # Bot should not respond

    # Case B: Reply Chain
    if is_reply_to_bot:
        log.info("Processing reply chain")
        current_message = message
        # Walk up the reply chain
        for i in range(15):  # Limit to 15 replies
            role = "assistant" if current_message.author == bot.user else "user"
            if current_message.content:
                entry = {"role": role, "content": current_message.content}
                if role == "user":
                    entry["author_details"] = get_author_details(current_message.author)
                history.insert(0, entry)  # Prepend to keep chronological order
                log.debug(
                    f"Added message {i+1} to reply chain: {current_message.content[:50]}..."
                )

            if not current_message.reference:
                log.debug("No more references in reply chain")
                break

            # Try to get the referenced message
            try:
                if current_message.reference.resolved:
                    # Use the resolved message if available
                    current_message = current_message.reference.resolved
                    log.debug("Using resolved reference")
                else:
                    # Fetch the message if not cached
                    current_message = await message.channel.fetch_message(
                        current_message.reference.message_id
                    )
                    log.debug("Fetched referenced message")
            except (discord.NotFound, discord.HTTPException) as e:
                log.debug(f"Could not fetch referenced message: {e}")
                break  # Stop if we can't find the message

        log.info(f"Reply Chain: Gathered {len(history)} messages for context")
        return history

    # Case A: Direct Mention
    if is_mention:
        log.info("Processing direct mention")
        # Remove both <@user_id> and <@!user_id> formats (the ! is for nicknames)
        prompt = (
            message.content.replace(f"<@{bot.user.id}>", "")
            .replace(f"<@!{bot.user.id}>", "")
            .strip()
        )
        log.debug(f"Cleaned mention prompt: '{prompt}'")

        if prompt:
            history.append(
                {
                    "role": "user",
                    "content": prompt,
                    "author_details": get_author_details(message.author),
                }
            )
            log.info(f"Direct Mention: Gathered 1 message for context")
        else:
            # Even if there's no text after the mention, we should still respond
            history.append(
                {
                    "role": "user",
                    "content": "Hello!",  # Default greeting when just mentioned
                    "author_details": get_author_details(message.author),
                }
            )
            log.info("Direct Mention: No text after mention, using default greeting")
        return history

    return None


# --- New Admin Commands ---
@bot.tree.command(
    name="set_ai_channel",
    description="Sets the current channel as an AI chat channel.",
)
@discord.app_commands.checks.has_permissions(administrator=True)
async def set_ai_channel(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    channel_id = interaction.channel_id
    guild_id = interaction.guild_id
    database.add_ai_channel(channel_id, guild_id)
    await interaction.followup.send(f"Channel <#{channel_id}> is now an AI channel.")


@bot.tree.command(
    name="remove_ai_channel",
    description="Removes the current channel from the AI chat list.",
)
@discord.app_commands.checks.has_permissions(administrator=True)
async def remove_ai_channel(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    channel_id = interaction.channel_id
    database.remove_ai_channel(channel_id)
    await interaction.followup.send(
        f"Channel <#{channel_id}> is no longer an AI channel."
    )


# --- Chat Command ---
@bot.tree.command(name="chat", description="Chat with the Yuna AI.")
@discord.app_commands.describe(prompt="The question or message for the AI.")
async def chat(interaction: discord.Interaction, prompt: str):
    """The primary chat command for Yuna."""
    await interaction.response.defer(thinking=True)

    try:
        # Get author details from the interaction
        author = interaction.user
        author_details = {
            "username": author.name,
            "display_name": author.display_name,
            "server_nickname": author.nick or author.display_name,
        }

        # Convert the single prompt into the structured conversation history
        history = [
            {"role": "user", "content": prompt, "author_details": author_details}
        ]

        response_text = await ai_manager.get_ai_response(history)
        await interaction.followup.send(f"> {prompt}\n\n{response_text}")

    except Exception as e:
        log.error(f"Error in /chat command: {e}", exc_info=True)
        await interaction.followup.send(
            "An unexpected error occurred. Please check the logs."
        )


# --- The main event listener ---
@bot.event
async def on_message(message: discord.Message):
    # Ignore messages from the bot itself
    if message.author == bot.user:
        log.debug(f"Ignoring message from bot itself: {message.content[:50]}...")
        return

    # Add debug logging
    log.info(
        f"Processing message from {message.author.name} in channel {message.channel.name}: {message.content[:100]}..."
    )

    # Remove the problematic command sync check - it prevents the bot from working
    # The bot should respond to messages regardless of command sync status

    conversation_history = await gather_context(message, bot)

    if conversation_history:
        log.info(
            f"Bot will respond - found {len(conversation_history)} messages in context"
        )
        async with message.channel.typing():
            try:
                response_text = await ai_manager.get_ai_response(conversation_history)
                # Reply to the user's message to maintain the thread
                if response_text:
                    await message.reply(response_text)
                    log.info(
                        f"Successfully replied to message from {message.author.name}"
                    )
                else:
                    log.warning("AI response was empty")
            except Exception as e:
                log.error(f"Error getting AI response: {e}", exc_info=True)
                await message.reply("Sorry, I encountered an error while thinking.")
    else:
        log.debug(
            f"Bot will not respond to message from {message.author.name} - no context gathered"
        )

    # We remove process_commands if we are moving away from prefix commands
    # await bot.process_commands(message)


def main():
    if not DISCORD_TOKEN:
        log.error("DISCORD_TOKEN not found in .env file.")
        return
    bot.run(DISCORD_TOKEN)


if __name__ == "__main__":
    main()
