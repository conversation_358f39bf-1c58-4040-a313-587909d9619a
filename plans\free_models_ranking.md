An analysis of Large Language Models (LLMs) available through various free and trial-based API services has been conducted, ranking them based on their `Artificial Analysis Intelligence Index` score. This index provides a comprehensive measure of a model's overall intelligence across different capabilities.

Below are the rankings of models for each provider. Models are sorted from highest to lowest score.

***

### **Free Providers**

#### **OpenRouter**
OpenRouter provides access to a wide variety of models, with the top-performing free models being OpenAI's `gpt-oss-120b` and DeepSeek's `DeepSeek R1`.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | openai/gpt-oss-120b:free | 57.9 |
| 2 | DeepSeek R1 | 52.0 |
| 3 | deepseek/deepseek-r1-0528:free | 52.0 |
| 4 | Kimi K2 | 48.1 |
| 5 | Llama 4 Maverick | 35.8 |
| 6 | z-ai/glm-4.5-air:free | 48.8 |
| 7 | Qwen QwQ 32B | 37.9 |
| 8 | Llama 4 Scout | 28.1 |
| 9 | Mistral Small 3.1 24B Instruct | 23.1 |
| 10 | Gemma 3 27B Instruct | 22.0 |
| 11 | Gemma 3 12B Instruct | 20.9 |
| 12 | Llama 3.1 405B Instruct | 25.7 |
| 13 | Gemma 2 9B Instruct | 17.2 |
| 14 | Gemma 3 4B Instruct | 14.8 |
| 15 | DeepHermes 3 Llama 3 8B Preview | 1.8 |
| 16 | Mistral 7B Instruct | 1.0 |

---

#### **Google AI Studio**
Google's own models are featured prominently, with `Gemini 2.5 Pro` leading the pack.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | Gemini 2.5 Pro | 59.6 |
| 2 | Gemini 2.0 Flash | 34.0 |
| 3 | Gemini 1.5 Flash | 24.4 |
| 4 | Gemma 3 27B Instruct | 22.0 |
| 5 | Gemma 3 12B Instruct | 20.9 |
| 6 | Gemini 1.5 Flash-8B | 16.3 |
| 7 | Gemma 3 4B Instruct | 14.8 |
| 8 | Gemma 3 1B Instruct | 6.1 |

---

#### **Cerebras**
Cerebras offers access to several high-performing models, including OpenAI's open-source model and Meta's Llama series.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | gpt-oss-120b | 57.9 |
| 2 | Qwen 3 235B A22B Instruct | 45.3 |
| 3 | Llama 4 Maverick | 35.8 |
| 4 | Qwen 3 32B | 26.4 |
| 5 | Llama 4 Scout | 28.1 |
| 6 | Llama 3.3 70B | 27.9 |
| 7 | Llama 3.1 8B | 16.9 |

---

#### **Groq**
Groq provides fast inference, with top-ranked models like `gpt-oss-120b` available.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | openai/gpt-oss-120b | 57.9 |
| 2 | moonshotai/kimi-k2-instruct-0905 | 50.4 |
| 3 | moonshotai/kimi-k2-instruct | 48.1 |
| 4 | Llama 4 Maverick 17B 128E Instruct | 35.8 |
| 5 | qwen/qwen3-32b | 26.4 |
| 6 | Llama 4 Scout Instruct | 28.1 |
| 7 | Llama 3.3 70B | 27.9 |
| 8 | Gemma 2 9B Instruct | 17.2 |
| 9 | Llama 3.1 8B | 16.9 |
| 10 | Llama 2 7B | 11.3 |

---

#### **Together (Free)**
This provider offers free access to a couple of powerful models.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | Llama 3.3 70B Instruct | 27.9 |

---

#### **Cohere**
Cohere's Command family of models is available directly through their platform.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | Command A | 28.1 |
| 2 | Command-R+ (Aug '24) | 7.1 |
| 3 | Command-R+ (Apr '24) | 5.5 |
| 4 | Aya Expanse 32B | 5.6 |
| 5 | Aya Expanse 8B | 1.6 |
| 6 | Command-R (Aug '24) | 1.0 |
| 7 | Command-R (Mar '24) | 1.0 |

---

#### **GitHub Models**
GitHub provides access to a vast catalog of models, including the latest from OpenAI and Meta.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | OpenAI gpt-5 | 66.7 |
| 2 | OpenAI o3 | 65.2 |
| 3 | OpenAI o4-mini | 59.0 |
| 4 | OpenAI o1 | 47.2 |
| 5 | OpenAI GPT-4.1 | 43.4 |
| 6 | OpenAI o3-mini | 48.1 |
| 7 | OpenAI o1-mini | 39.2 |
| 8 | Grok 3 | 36.0 |
| 9 | OpenAI GPT-4o (March 2025, chatgpt-4o-latest) | 35.6 |
| 10| Llama 4 Maverick 17B 128E Instruct FP8 | 35.8 |
| 11| Meta-Llama-3.1-405B-Instruct | 25.7 |
| 12| Llama 4 Scout 17B 16E Instruct | 28.1 |
| 13| Llama-3.3-70B-Instruct | 27.9 |
| 14| OpenAI GPT-4o | 27.0 |
| 15| Phi-4 | 24.6 |
| 16| Meta-Llama-3.1-8B-Instruct | 16.9 |
| 17| Phi-4-mini-instruct | 15.7 |
| 18| Llama-3.2-11B-Vision-Instruct | 14.6 |
| 19| Phi-3-mini instruct (128k) | 13.1 |
| 20| Phi-3-mini instruct (4k) | 13.1 |
| 21| Llama-3.2-3B-Instruct | 11.2 |
| 22| Phi-3-medium instruct (128k) | 10.1 |
| 23| Phi-3-medium instruct (4k) | 10.1 |
| 24| Ministral 3B | 5.2 |

---

#### **Cloudflare Workers AI**
Cloudflare offers a serverless platform with access to many efficient open-source models.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | @cf/openai/gpt-oss-120b | 57.9 |
| 2 | Qwen QwQ 32B | 37.9 |
| 3 | Llama 4 Scout Instruct | 28.1 |
| 4 | Llama 3.3 70B Instruct (FP8) | 27.9 |
| 5 | Mistral Small 3.1 24B Instruct | 23.1 |
| 6 | Llama 3.2 11B Vision Instruct | 14.6 |
| 7 | Llama 2 13B Chat (AWQ) | 5.5 |
| 8 | Llama 3 8B Instruct | 16.9 |
| 9 | Llama 3 8B Instruct | 16.9 |
| 10 | Llama 3 8B Instruct (AWQ) | 16.9 |
| 11 | Llama 3.1 8B Instruct (AWQ) | 16.9 |
| 12 | Llama 3.1 8B Instruct (FP8) | 16.9 |
| 13 | Llama 2 7B Chat (FP16) | 11.3 |
| 14 | Llama 2 7B Chat (INT8) | 11.3 |
| 15 | Llama 2 7B Chat (LoRA) | 11.3 |
| 16 | Llama 3.2 3B Instruct | 11.2 |
| 17 | Mistral 7B Instruct v0.1 | 1.0 |
| 18 | Mistral 7B Instruct v0.1 (AWQ) | 1.0 |
| 19 | Mistral 7B Instruct v0.2 | 1.0 |
| 20 | Mistral 7B Instruct v0.2 (LoRA) | 1.0 |
| 21 | Llama 3.2 1B Instruct | 7.1 |

---

#### **Google Cloud Vertex AI**
Provides free preview access to some of the latest models from Meta.

| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | Llama 3.2 90B Vision Instruct | 18.9 |
| 2 | Llama 3.1 70B Instruct | 22.7 |
| 3 | Llama 3.1 8B Instruct | 16.9 |

***

### **Providers with Trial Credits**

The following providers offer trial credits, giving users a chance to experiment with various models.

#### **Hyperbolic**
| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | Llama 4 Maverick 17B 128E Instruct FP8 | 35.8 |
| 2 | DeepSeek V3 0324 | 41.3 |
| 3 | Qwen QwQ 32B | 37.9 |
| 4 | Qwen QwQ 32B Preview | 28.0 |
| 5 | DeepSeek V3 | 33.2 |
| 6 | Hermes 3 Llama 3.1 70B | 14.7 |
| 7 | Llama 3.1 405B Instruct | 25.7 |
| 8 | Llama 3.1 70B Instruct | 22.7 |
| 9 | Llama 3.3 70B Instruct | 27.9 |
| 10| Llama 3.1 8B Instruct | 16.9 |
| 11| Qwen2.5 72B Instruct | 27.9 |
| 12| Llama 3.2 3B Instruct | 11.2 |
| 13| Pixtral 12B (2409) | 8.9 |

#### **SambaNova Cloud**
| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | deepseek-ai/DeepSeek-V3.1 | 44.8 |
| 2 | deepseek-ai/DeepSeek-V3-0324 | 41.3 |
| 3 | Llama-4-Maverick-17B-128E-Instruct | 35.8 |
| 4 | Llama 3.3 70B | 27.9 |
| 5 | Llama 3.3 70B | 27.9 |
| 6 | Qwen/Qwen3-32B | 26.4 |
| 7 | Llama 3.1 8B | 16.9 |

#### **Scaleway Generative APIs**
| Rank | Model Name | Artificial Analysis Intelligence Index |
| :--- | :--- | :--- |
| 1 | gpt-oss-120b | 57.9 |
| 2 | qwen3-235b-a22b-instruct-2507 | 45.3 |
| 3 | Llama 3.3 70B Instruct | 27.9 |
| 4 | qwen3-coder-30b-a3b-instruct | 33.4 |
| 5 | Gemma 3 27B Instruct | 22.0 |
| 6 | Mistral Small 3.1 24B Instruct 2503 | 23.1 |
| 7 | Llama 3.1 8B Instruct | 16.9 |
| 8 | Pixtral 12B (2409) | 8.9 |

***

### Unranked Models

Several models listed across the services could not be ranked due to missing data in the `models.json` file or because they are specialized models (e.g., for speech or code generation) not covered by the `Artificial Analysis Intelligence Index`. These include:

*   **Generic Listings**: "Various open models" (from NVIDIA NIM, HuggingFace Inference Providers).
*   **Speech & Audio**: Whisper Large v3, Whisper Large v3 Turbo (from Groq).
*   **Specialized/Untracked**: Models like `DeepCoder`, `Dolphin`, `Shisa`, `Mai-DS-R1`, `Tencent Hunyuan`, `Llama Guard`, and others which did not have a corresponding entry or score in the provided JSON data.
*   **Trial Credit Providers**: Many models from providers like Fireworks, Baseten, Nebius, Novita, etc., are not individually listed in the prompt and thus cannot be cross-referenced and ranked.