import json
import re


def get_free_models(readme_content):
    free_models = set()

    # Regex to find model names in markdown links (e.g., OpenRouter)
    # Example: [DeepCoder 14B Preview](...)
    link_regex = re.compile(r"\[([^\]]+)\]\(https?://[^\)]+\)")
    for match in link_regex.findall(readme_content):
        free_models.add(match.lower().strip())

    # Regex for table rows (e.g., Google AI Studio, Groq)
    # This will capture the first column content.
    # Example: | Gemini 2.5 Pro | ... |
    table_row_regex = re.compile(r"\|\s*([^\|]+?)\s*\|")
    for match in table_row_regex.findall(readme_content):
        model_name = match.strip()
        # Basic filtering to avoid headers and separators
        if (
            "model name" not in model_name.lower()
            and "---" not in model_name
            and model_name
        ):
            # Handle potential HTML links inside tables
            link_match = re.search(r"<a[^>]*>([^<]+)</a>", model_name)
            if link_match:
                model_name = link_match.group(1).strip()
            free_models.add(model_name.lower().strip())

    # Regex for Cloudflare models
    # Example: - @cf/openai/gpt-oss-120b
    cf_model_regex = re.compile(r"- @cf/([a-zA-Z0-9\-_/]+)")
    for match in cf_model_regex.findall(readme_content):
        # The model name in models.json is just the part after the slash
        free_models.add(match.split("/")[-1].lower().strip())

    # Regex for simple list items (e.g., Cohere, GitHub)
    # Example: - Command-R+
    list_item_regex = re.compile(r"^\s*-\s*(.*)", re.MULTILINE)
    for match in list_item_regex.findall(readme_content):
        model_name = match.strip()
        # Filter out non-model names
        if (
            "requests/" not in model_name
            and "tokens/" not in model_name
            and "[" not in model_name
            and not model_name.startswith("@cf")
        ):
            free_models.add(model_name.lower().strip())

    return free_models


def rank_free_models():
    with open("models.json", "r", encoding="utf-8") as f:
        models_data = json.load(f)

    with open("plans/Free LLM API resources.md", "r", encoding="utf-8") as f:
        readme_content = f.read()

    free_model_names = get_free_models(readme_content)

    ranked_models = []
    for model in models_data["data"]:
        model_name_lower = model["name"].lower().strip()
        # Some names in the free list have extra details, so check if the ranked name is a substring
        if model_name_lower in free_model_names:
            ranked_models.append(model)
        else:
            for free_name in free_model_names:
                if model_name_lower in free_name:
                    ranked_models.append(model)
                    break  # prevent duplicates

    # Remove duplicates by ID
    seen_ids = set()
    unique_ranked_models = []
    for model in ranked_models:
        if model["id"] not in seen_ids:
            unique_ranked_models.append(model)
            seen_ids.add(model["id"])

    # Sort by intelligence index, putting models with no score at the end
    unique_ranked_models.sort(
        key=lambda x: (
            x["evaluations"]["artificial_analysis_intelligence_index"]
            if x["evaluations"]["artificial_analysis_intelligence_index"] is not None
            else -1
        ),
        reverse=True,
    )

    # Create markdown file
    with open("free_models_ranking.md", "w", encoding="utf-8") as f:
        f.write("# Ranked Free LLM Models\n\n")
        f.write(
            "This list is generated by cross-referencing a list of free model providers with a comprehensive model performance ranking.\n\n"
        )
        f.write("| Rank | Model Name | Intelligence Index | Provider |\n")
        f.write("|------|------------|--------------------|----------|\n")
        for i, model in enumerate(unique_ranked_models):
            provider = model.get("model_creator", {}).get("name", "N/A")
            index = model["evaluations"]["artificial_analysis_intelligence_index"]
            f.write(
                f"| {i+1} | {model['name']} | {index if index is not None else 'N/A'} | {provider} |\n"
            )


if __name__ == "__main__":
    rank_free_models()
