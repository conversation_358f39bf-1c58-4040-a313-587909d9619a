# Phase 1: Foundation & The First Conversation

**Goal:** Get a minimally viable bot running that can respond to a slash command using a single, hardcoded AI provider. This phase is about building the skeleton of the application.

---

### **Key Objectives**

1.  **Project Setup:** Create the file structure and environment.
2.  **Basic Bot Connection:** Get the bot to appear online in Discord.
3.  **Database Initialization:** Create a simple SQLite database to store an API key.
4.  **Single Provider Integration:** Write the code to communicate with one AI service (Groq is a good first choice for its speed).
5.  **Simple Command:** Create a `/chat` command that takes a user's question and returns an AI-generated answer.

### **Step-by-Step Implementation Plan**

#### **Step 1: Project Structure and Setup**

1.  Create the main project directory (e.g., `yuna-bot`).
2.  Inside it, create the following file structure:

    ```
    yuna-bot/
    ├── providers/
    │   └── __init__.py
    │   └── groq.py
    ├── .env
    ├── .gitignore
    ├── ai_manager.py
    ├── database.py
    └── yuna_bot.py
    ```

3.  Set up a Python virtual environment:
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    ```

4.  Install the necessary libraries:
    ```bash
    pip install discord.py python-dotenv aiohttp
    ```

5.  Populate `.gitignore` with common Python and environment files:
    ```
    venv/
    __pycache__/
    *.pyc
    .env
    ```

6.  In your `.env` file, add your Discord Bot Token:
    ```
    DISCORD_TOKEN=YourDiscordBotTokenGoesHere
    ```

#### **Step 2: Database Initialization (`database.py`)**

This file will handle all interactions with the SQLite database. For now, it just needs to create the `api_keys` table and provide a way to get a key.

```python
# database.py
import sqlite3

DB_NAME = "yuna.db"

def initialize_database():
    """Creates the database and necessary tables if they don't exist."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    # Table for storing API keys for different services
    cur.execute('''
        CREATE TABLE IF NOT EXISTS api_keys (
            service_name TEXT PRIMARY KEY,
            api_key TEXT NOT NULL
        )
    ''')
    con.commit()
    con.close()
    print("Database initialized.")

def get_api_key(service_name):
    """Retrieves an API key for a given service."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("SELECT api_key FROM api_keys WHERE service_name = ?", (service_name,))
    result = cur.fetchone()
    con.close()
    return result[0] if result else None

# You can add a function to insert keys manually for now
def add_api_key(service_name, api_key):
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("INSERT OR REPLACE INTO api_keys (service_name, api_key) VALUES (?, ?)", (service_name, api_key))
    con.commit()
    con.close()
```

#### **Step 3: First Provider Module (`providers/groq.py`)**

This module contains the specific logic for calling the Groq API.

```python
# providers/groq.py
import aiohttp

API_URL = "https://api.groq.com/openai/v1/chat/completions"

async def generate(api_key, conversation_history):
    """
    Generates a response from the Groq API.
    conversation_history is expected to be a list of dicts,
    but for Phase 1, we'll just handle a single prompt.
    """
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # In later phases, conversation_history will be the direct payload.
    # For now, we construct it from a single string.
    user_prompt = conversation_history[0]['content']

    payload = {
        "model": "llama3-8b-8192", # A common, fast model
        "messages": [{"role": "user", "content": user_prompt}]
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(API_URL, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return data['choices'][0]['message']['content']
            else:
                # Basic error handling for now
                error_text = await response.text()
                raise Exception(f"Groq API Error: {response.status} - {error_text}")

```

#### **Step 4: Initial AI Manager (`ai_manager.py`)**

This manager is very simple for now. It knows about one provider and how to call it.

```python
# ai_manager.py
import database
from providers import groq # Import our groq module

async def get_ai_response(prompt_text):
    """
    The main entry point for getting an AI response.
    For Phase 1, this is simple. It will become complex later.
    """
    groq_api_key = database.get_api_key("groq")
    if not groq_api_key:
        return "Error: Groq API key is not configured."

    # For Phase 1, we convert the simple string prompt into the
    # list-of-dicts structure our provider expects.
    history_for_provider = [{'role': 'user', 'content': prompt_text}]

    try:
        response = await groq.generate(groq_api_key, history_for_provider)
        return response
    except Exception as e:
        print(f"Error calling AI provider: {e}")
        return "Sorry, I encountered an error while trying to think."
```

#### **Step 5: The Discord Bot (`yuna_bot.py`)**

This file ties everything together. It connects to Discord, defines the `/chat` command, and calls the `ai_manager`.

```python
# yuna_bot.py
import os
import discord
from discord.ext import commands
from dotenv import load_dotenv
import database
import ai_manager

# Load environment variables
load_dotenv()
DISCORD_TOKEN = os.getenv("DISCORD_TOKEN")

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="/", intents=intents)

@bot.event
async def on_ready():
    """Event that fires when the bot is connected and ready."""
    print(f'{bot.user} has connected to Discord!')
    print("Initializing database...")
    database.initialize_database()
    # After initializing, you should manually add your Groq key
    # For example: database.add_api_key("groq", "YourGroqKeyHere")
    # You only need to run this line once.
    print("Syncing slash commands...")
    await bot.tree.sync()
    print("Yuna is ready.")


@bot.tree.command(name="chat", description="Chat with the Yuna AI.")
async def chat(interaction: discord.Interaction, *, prompt: str):
    """The primary chat command for Yuna."""
    # Acknowledge the command immediately to prevent "interaction failed"
    await interaction.response.defer(thinking=True)
    
    try:
        # Get the response from the AI manager
        response_text = await ai_manager.get_ai_response(prompt)
        
        # Send the response
        await interaction.followup.send(f"> {prompt}\n\n{response_text}")

    except Exception as e:
        print(f"Error in /chat command: {e}")
        await interaction.followup.send("An unexpected error occurred. Please check the logs.")


def main():
    if not DISCORD_TOKEN:
        print("Error: DISCORD_TOKEN not found in .env file.")
        return
    bot.run(DISCORD_TOKEN)

if __name__ == "__main__":
    main()
```

### **Definition of Done for Phase 1**

-   [ ] The project structure is created correctly.
-   [ ] The bot starts without errors using `python yuna_bot.py`.
-   [ ] The bot appears as "Online" in your Discord server.
-   [ ] The `yuna.db` file is created upon first run.
-   [ ] You have manually added your Groq API key to the database.
-   [ ] The `/chat` command is visible and usable in Discord.
-   [ ] Using `/chat prompt: "Hello, world!"` results in a response from the Groq LLaMA model.
