#!/usr/bin/env python3
"""
Test script for the Discord bot enhancements:
1. Model attribution in responses
2. Manual model selection for admins
3. Cooldown system validation
"""

import asyncio
import logging
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import database
import ai_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

async def test_model_attribution():
    """Test that AI responses include model attribution."""
    log.info("🧪 Testing model attribution...")
    
    # Initialize database
    database.initialize_database()
    
    # Create test conversation
    test_history = [
        {
            "role": "user", 
            "content": "Hello, this is a test message",
            "author_details": {
                "username": "testuser",
                "display_name": "Test User",
                "server_nickname": "Test User"
            }
        }
    ]
    
    # Get AI response
    response = await ai_manager.get_ai_response(test_history)
    
    # Verify response structure
    assert isinstance(response, dict), "Response should be a dictionary"
    assert "content" in response, "Response should have 'content' key"
    assert "model" in response, "Response should have 'model' key"
    
    log.info(f"✅ Response structure correct: {response['model']}")
    log.info(f"Content preview: {response['content'][:50]}...")
    
    return response

def test_database_preferred_models():
    """Test preferred model database functions."""
    log.info("🧪 Testing preferred model database functions...")
    
    # Initialize database
    database.initialize_database()
    
    # Test setting preferred model
    guild_id = 12345
    provider_name = "gemini"
    model_name = "gemini/gemini-2.5-flash"
    user_id = 67890
    
    database.set_preferred_model(guild_id, provider_name, model_name, user_id)
    
    # Test getting preferred model
    preferred = database.get_preferred_model(guild_id)
    assert preferred is not None, "Should retrieve preferred model"
    assert preferred["provider_name"] == provider_name
    assert preferred["model_name"] == model_name
    assert preferred["set_by_user_id"] == user_id
    
    log.info(f"✅ Preferred model set and retrieved: {preferred}")
    
    # Test clearing preferred model
    database.clear_preferred_model(guild_id)
    cleared = database.get_preferred_model(guild_id)
    assert cleared is None, "Preferred model should be cleared"
    
    log.info("✅ Preferred model cleared successfully")

async def test_preferred_model_override():
    """Test that preferred model override works in AI manager."""
    log.info("🧪 Testing preferred model override...")
    
    # Initialize database
    database.initialize_database()
    
    # Set a preferred model
    guild_id = 12345
    database.set_preferred_model(guild_id, "cerebras", "cerebras/qwen-3-235b-a22b", 67890)
    
    # Create test conversation
    test_history = [
        {
            "role": "user", 
            "content": "Test preferred model",
            "author_details": {
                "username": "testuser",
                "display_name": "Test User", 
                "server_nickname": "Test User"
            }
        }
    ]
    
    # Get AI response with guild_id
    response = await ai_manager.get_ai_response(test_history, guild_id)
    
    log.info(f"✅ Response with preferred model: {response['model']}")
    
    # Clean up
    database.clear_preferred_model(guild_id)
    
    return response

def test_cooldown_system():
    """Test that the cooldown system is working correctly."""
    log.info("🧪 Testing cooldown system...")
    
    # Initialize database
    database.initialize_database()
    
    # Test setting cooldowns
    database.set_cooldown("test_provider", 60)
    
    # Test checking cooldowns
    assert database.is_on_cooldown("test_provider") == True, "Provider should be on cooldown"
    assert database.is_on_cooldown("nonexistent_provider") == False, "Non-existent provider should not be on cooldown"
    
    # Test cooldown status
    status = database.get_cooldown_status()
    assert "test_provider" in status, "Test provider should be in cooldown status"
    assert status["test_provider"]["on_cooldown"] == True, "Test provider should be on cooldown"
    
    log.info(f"✅ Cooldown system working: {status}")
    
    # Test clearing cooldown
    database.clear_expired_cooldown("test_provider")
    assert database.is_on_cooldown("test_provider") == False, "Provider should no longer be on cooldown"
    
    log.info("✅ Cooldown clearing works correctly")

def test_provider_configuration():
    """Test that all providers are properly configured."""
    log.info("🧪 Testing provider configuration...")
    
    providers = ai_manager.PROVIDER_PRIORITY
    assert len(providers) == 6, f"Should have 6 providers, found {len(providers)}"
    
    required_keys = ["name", "model", "score", "description"]
    for provider in providers:
        for key in required_keys:
            assert key in provider, f"Provider {provider.get('name', 'unknown')} missing key: {key}"
        
        # Verify model format
        assert "/" in provider["model"], f"Model should include provider prefix: {provider['model']}"
    
    log.info(f"✅ All {len(providers)} providers properly configured")
    
    # Test provider status functions
    status = ai_manager.get_provider_status()
    assert "total_providers" in status
    assert "available_providers" in status
    assert "providers" in status
    
    summary = ai_manager.get_provider_summary()
    assert "Provider Status" in summary
    
    log.info("✅ Provider status functions working")

async def main():
    """Run all enhancement tests."""
    log.info("🚀 Starting Discord Bot Enhancement Tests")
    log.info("=" * 60)
    
    try:
        # Test 1: Model Attribution
        await test_model_attribution()
        
        # Test 2: Preferred Model Database Functions
        test_database_preferred_models()
        
        # Test 3: Preferred Model Override
        await test_preferred_model_override()
        
        # Test 4: Cooldown System Validation
        test_cooldown_system()
        
        # Test 5: Provider Configuration
        test_provider_configuration()
        
        log.info("=" * 60)
        log.info("🎉 All enhancement tests completed successfully!")
        log.info("✅ Model attribution working")
        log.info("✅ Manual model selection working")
        log.info("✅ Cooldown system validated")
        log.info("✅ Provider configuration verified")
        
    except Exception as e:
        log.error(f"❌ Test failed: {e}", exc_info=True)
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
