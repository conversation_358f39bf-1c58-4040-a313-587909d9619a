# Phase 3: Resilience & Expansion

**Goal:** Transform Yuna from a single-provider bot into a resilient, multi-provider system. This phase implements the core fallback logic, rate limit tracking, and cooldown mechanism, making the bot robust against API failures.

---

### **Key Objectives**

1.  **Expand Database:** Add tables for tracking rate limit cooldowns and usage statistics.
2.  **Architect Advanced AI Manager:** Rebuild the AI manager to handle a prioritized list of providers and a fallback loop.
3.  **Implement Cooldown Logic:** When an API returns a rate limit error (HTTP 429), the bot should automatically sideline that provider for a set duration.
4.  **Add More Providers:** Integrate at least two new AI services (e.g., Google Gemini, Cohere) into the system, ensuring they follow a standard interface.

### **Step-by-Step Implementation Plan**

#### **Step 1: Database Expansion (`database.py`)**

Add the `rate_limit_cooldowns` table. The `usage_stats` table can be considered optional for now if you want to simplify, but the cooldown table is essential.

```python
# database.py additions

def initialize_database():
    # ... (existing code for api_keys and ai_channels) ...
    cur.execute('''
        CREATE TABLE IF NOT EXISTS rate_limit_cooldowns (
            service_name TEXT PRIMARY KEY,
            cooldown_until INTEGER NOT NULL
        )
    ''')
    con.commit()
    con.close()
    print("Database initialized.")

def set_cooldown(service_name, cooldown_seconds):
    """Sets a cooldown for a specific service."""
    import time
    cooldown_until = int(time.time()) + cooldown_seconds
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("INSERT OR REPLACE INTO rate_limit_cooldowns (service_name, cooldown_until) VALUES (?, ?)", (service_name, cooldown_until))
    con.commit()
    con.close()

def is_on_cooldown(service_name):
    """Checks if a service is currently on cooldown."""
    import time
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("SELECT cooldown_until FROM rate_limit_cooldowns WHERE service_name = ?", (service_name,))
    result = cur.fetchone()
    con.close()
    if result:
        return time.time() < result[0]
    return False

```

#### **Step 2: Create New Provider Modules (`providers/` directory)**

We need more "drivers" for our system. Each must conform to the `generate_from_messages` interface.

**`providers/gemini.py` (Example)**

```python
# providers/gemini.py
import aiohttp
import json

API_URL_TEMPLATE = "https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"

async def generate_from_messages(api_key, formatted_messages, model="gemini-1.5-flash"):
    """Generates a response from the Google Gemini API."""
    url = API_URL_TEMPLATE.format(model=model, api_key=api_key)
    
    # Gemini has a different format for conversation history
    # It alternates between 'user' and 'model' roles.
    gemini_contents = []
    for msg in formatted_messages:
        # Gemini uses 'model' for assistant's role
        role = 'model' if msg['role'] == 'assistant' else 'user'
        gemini_contents.append({'role': role, 'parts': [{'text': msg['content']}]})

    payload = {'contents': gemini_contents}

    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return data['candidates'][0]['content']['parts'][0]['text']
            elif response.status == 429:
                # Specific error for rate limiting
                raise RateLimitException("Gemini API rate limit exceeded.")
            else:
                error_text = await response.text()
                raise Exception(f"Gemini API Error: {response.status} - {error_text}")

# Custom exception for easy catching
class RateLimitException(Exception):
    pass
```
*Note: You would create a similar file for Cohere, OpenRouter, etc., each handling its own specific API format.*

#### **Step 3: The Resilient AI Manager (`ai_manager.py`)**

This is the most critical part of this phase. The manager becomes a true orchestrator.

```python
# ai_manager.py - Major Overhaul

import database
# Import all your provider modules and the new exception
from providers import groq, gemini 

# Define our provider priority list
# The bot will try them in this order.
PROVIDER_PRIORITY = [
    {"name": "groq", "module": groq, "model": "llama3-8b-8192"},
    {"name": "gemini", "module": gemini, "model": "gemini-1.5-flash"},
    # Add other providers here, e.g., cohere
]
DEFAULT_COOLDOWN_SECONDS = 60

async def get_ai_response(conversation_history):
    """
    Orchestrates fetching an AI response, handling fallbacks and cooldowns.
    """
    system_prompt = "You are Yuna, a helpful AI assistant on Discord..."
    formatted_messages = [{"role": "system", "content": system_prompt}]
    # ... (the context formatting logic from Phase 2 remains the same) ...
    for message in conversation_history:
        if message['role'] == 'user':
            #...
            formatted_messages.append(...)
        else: # role == 'assistant'
            formatted_messages.append(...)

    # The new fallback loop
    for provider in PROVIDER_PRIORITY:
        service_name = provider['name']
        
        if database.is_on_cooldown(service_name):
            print(f"Service '{service_name}' is on cooldown. Skipping.")
            continue # Skip to the next provider

        print(f"Attempting to use service: {service_name}")
        api_key = database.get_api_key(service_name)
        if not api_key:
            print(f"Warning: API key for '{service_name}' not found. Skipping.")
            continue

        try:
            # Call the provider's generate function
            response = await provider['module'].generate_from_messages(api_key, formatted_messages)
            print(f"Successfully received response from {service_name}.")
            return response # Success! Return the response.

        except gemini.RateLimitException as e: # Catch the specific rate limit error
            print(f"RATE LIMIT HIT for {service_name}: {e}")
            database.set_cooldown(service_name, DEFAULT_COOLDOWN_SECONDS)
            # Don't return, let the loop try the next provider
        
        except Exception as e:
            # Handle other errors (e.g., 500 internal server error)
            print(f"ERROR with {service_name}: {e}")
            # Optional: you could also set a shorter cooldown for general errors
            # database.set_cooldown(service_name, 30) 
            continue # Try the next provider
    
    # If the loop completes without returning, all providers have failed.
    return "I'm sorry, all of my AI thinking-cores are currently unavailable. Please try again in a moment."
```
*Note: You must remember to add the new API keys (e.g., for Gemini) to your database using the `add_api_key` function from Phase 1.*

### **Definition of Done for Phase 3**

-   [ ] The database schema is updated with the `rate_limit_cooldowns` table.
-   [ ] At least one new provider module (e.g., `gemini.py`) has been created and tested.
-   [ ] The `ai_manager.py` file has been refactored to include the provider priority list and the fallback loop.
-   [ ] The bot successfully gets a response from the first provider in the list.
-   [ ] **Crucial Test:** When a provider hits a rate limit (you may need to simulate this by spamming requests), the logs show that it is put on cooldown and the bot automatically tries the *next* provider in the list.
-   [ ] If all providers fail, the bot sends a user-friendly error message to Discord.
-   [ ] The bot does not attempt to use a provider that is on cooldown.