# database.py
import sqlite3
import logging
from typing import Optional

log = logging.getLogger(__name__)

DB_NAME = "yuna.db"


def initialize_database() -> None:
    """Creates the database and necessary tables if they don't exist."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    # Table for storing designated AI channels
    cur.execute(
        """
        CREATE TABLE IF NOT EXISTS ai_channels (
            channel_id INTEGER PRIMARY KEY,
            guild_id INTEGER NOT NULL
        )
    """
    )
    con.commit()
    con.close()
    log.info("Database initialized.")


def add_ai_channel(channel_id: int, guild_id: int) -> None:
    """Adds a channel to the list of AI-enabled channels."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute(
        "INSERT OR REPLACE INTO ai_channels (channel_id, guild_id) VALUES (?, ?)",
        (channel_id, guild_id),
    )
    con.commit()
    con.close()


def remove_ai_channel(channel_id: int) -> None:
    """Removes a channel from the list of AI-enabled channels."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("DELETE FROM ai_channels WHERE channel_id = ?", (channel_id,))
    con.commit()
    con.close()


def is_ai_channel(channel_id: int) -> bool:
    """Checks if a channel is an AI-enabled channel."""
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("SELECT 1 FROM ai_channels WHERE channel_id = ?", (channel_id,))
    result = cur.fetchone()
    con.close()
    return result is not None
