# Phase 4: Refinement, Logging & Deployment

**Goal:** Solidify the bot's codebase, making it robust, maintainable, and ready for continuous, long-term operation. This phase focuses on production-readiness.

---

### **Key Objectives**

1.  **Implement Robust Logging:** Move from `print()` statements to a structured logging system to capture events and errors for easier debugging.
2.  **Enhance User-Facing Errors:** Provide more specific and helpful error messages to the user.
3.  **Code Cleanup and Documentation:** Refactor code, add comments, and create docstrings to improve maintainability.
4.  **Deployment Preparation:** Create the necessary files (`requirements.txt`, `README.md`) for easy setup and deployment on a server.

### **Step-by-Step Implementation Plan**

#### **Step 1: Implementing Structured Logging**

Create a `logging_setup.py` file to configure the logger, then import and use it throughout the application.

**`logging_setup.py`**
```python
# logging_setup.py
import logging
import sys

def setup_logging():
    """Configures the root logger for the application."""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Stream Handler (for console output)
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    # File Handler (to log to a file)
    file_handler = logging.FileHandler('yuna.log')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    print("Logging configured.")
```

**Modify `yuna_bot.py` to use logging:**
```python
# yuna_bot.py
import logging
from logging_setup import setup_logging
# ... other imports

# Call setup at the very beginning
setup_logging()
log = logging.getLogger(__name__)

# Replace print statements with log statements
@bot.event
async def on_ready():
    log.info(f'{bot.user} has connected to Discord!')
    log.info("Initializing database...")
    database.initialize_database()
    log.info("Syncing slash commands...")
    await bot.tree.sync()
    log.info("Yuna is ready.")

# In on_message and commands:
# except Exception as e:
#     log.error(f"Error getting AI response: {e}", exc_info=True) # exc_info adds traceback
#     await message.reply("...")```
**Modify `ai_manager.py` and other files similarly:**
```python
# ai_manager.py
import logging
log = logging.getLogger(__name__)

# Replace print() with:
# log.info(f"Attempting to use service: {service_name}")
# log.warning(f"RATE LIMIT HIT for {service_name}: {e}")
# log.error(f"ERROR with {service_name}: {e}")
```

#### **Step 2: Enhanced User-Facing Errors**

Improve the messages sent to the user in Discord when something goes wrong.

**In `yuna_bot.py` (`on_message` handler):**
```python
# ... inside the `on_message` event
try:
    response_text = await ai_manager.get_ai_response(conversation_history)
    if response_text:
        await message.reply(response_text)
    else:
        # ai_manager might return None or a specific error object
        await message.reply("I'm sorry, I couldn't come up with a response. All my thinking-cores might be busy. Please try again in a moment.")
except Exception as e:
    log.error("An unhandled exception occurred in on_message", exc_info=True)
    await message.reply("Oh no! A critical error occurred. I've notified my developers.")
```

#### **Step 3: Code Cleanup and Documentation**

Go through every file and perform the following actions:
1.  **Add Docstrings:** Explain what each function and class does, its parameters, and what it returns.
2.  **Add Inline Comments:** Clarify any complex or non-obvious lines of code.
3.  **Refactor:** Look for repeated code that could be turned into a helper function. Ensure variable names are clear and descriptive.
4.  **Type Hinting:** Add type hints to function signatures (`def my_func(name: str) -> bool:`) to improve readability and allow for static analysis.

**Example of a well-documented function:**```python
# database.py
def is_on_cooldown(service_name: str) -> bool:
    """
    Checks if a service is currently on cooldown by comparing the current
    Unix timestamp with the stored 'cooldown_until' timestamp.

    Args:
        service_name: The name of the service to check (e.g., 'groq').

    Returns:
        True if the service is on cooldown, False otherwise.
    """
    import time
    # ... function logic
```

#### **Step 4: Deployment Preparation**

1.  **Create `requirements.txt`:** Freeze your project's dependencies into a file so they can be easily installed on a server.
    ```bash
    pip freeze > requirements.txt
    ```

2.  **Create a master `README.md` file:** This is the entry point for anyone (including your future self) who wants to understand and run the project.

    **`README.md` Template:**
    ```markdown
    # Yuna AI Discord Bot

    Yuna is a modular, multi-provider AI chatbot for Discord designed for resilience against the rate limits of free-tier AI API services.

    ## Features

    - Intelligent Rate Limit Management with automatic fallbacks.
    - Context-aware conversations via mentions, replies, and dedicated AI channels.
    - Supports multiple AI providers (Groq, Google Gemini, etc.).
    - Configurable on a per-server basis.

    ## Setup and Installation

    ### Prerequisites
    - Python 3.10+
    - A Discord Bot Application and Token

    ### Installation Steps

    1.  **Clone the repository:**
        ```bash
        git clone https://your-repo-url/yuna-bot.git
        cd yuna-bot
        ```

    2.  **Create a virtual environment and install dependencies:**
        ```bash
        python -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        ```

    3.  **Configure Environment Variables:**
        Create a `.env` file in the root directory and add your Discord token:
        ```
        DISCORD_TOKEN=YourDiscordBotTokenGoesHere
        ```

    4.  **Configure API Keys:**
        Run the bot once to initialize the database (`yuna.db`). Then, you will need to manually add your AI provider API keys. You can do this with a separate script or by temporarily uncommenting `database.add_api_key()` lines in `yuna_bot.py`.
        
        Example: `database.add_api_key("groq", "gsk_YourGroqAPIKey")`
        
    5.  **Run the bot:**
        ```bash
        python yuna_bot.py
        ```

    ## Usage

    - **Chat:** Mention `@Yuna`, reply to her messages, or chat in a designated AI channel.
    - **Admin Commands:**
        - `/set_ai_channel`: Sets the current channel as a full-time AI chat room.
        - `/remove_ai_channel`: Reverts an AI channel to a normal channel.
    ```

### **Definition of Done for Phase 4**

-   [ ] All `print()` statements have been replaced with structured `logging` calls.
-   [ ] A `yuna.log` file is created and populated with logs when the bot runs.
-   [ ] User-facing error messages are helpful and do not expose internal error details.
-   [ ] All major functions have docstrings and type hints.
-   [ ] A comprehensive `README.md` exists in the project root.
-   [ ] A `requirements.txt` file accurately lists all project dependencies.
-   [ ] The bot is running successfully on a test server with no obvious bugs.