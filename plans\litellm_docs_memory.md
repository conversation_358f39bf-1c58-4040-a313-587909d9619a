# LiteLLM Documentation Memory Dump

This document contains key information and code snippets from the LiteLLM documentation to serve as a quick reference.

## Key Concepts

*   **Unified Interface:** LiteLLM provides a single function, `litellm.completion()` (and `litellm.acompletion()` for async), to interact with over 100 LLM providers.
*   **API Keys:** LiteLLM automatically reads API keys from environment variables (e.g., `OPENAI_API_KEY`, `GROQ_API_KEY`). No manual key management is needed in the code.
*   **Model Names:** Models are specified with a string that includes the provider, e.g., `"groq/llama3-8b-8192"`.

## Code Snippets

### Basic Completion Call

To call a single model:

```python
import litellm

response = await litellm.acompletion(
    model="groq/llama3-8b-8192",
    messages=[{"role": "user", "content": "Hey, how's it going?"}]
)

print(response.choices[0].message.content)
```

### Fallback (Resilience)

To make your calls resilient, provide a list of models to the `acompletion` function. LiteLLM will try them in order until one succeeds.

**Important:** When using a list of models for fallback, you **must not** include the `model` parameter in the call. The `models` parameter takes its place.

```python
import litellm

response = await litellm.acompletion(
    models=["groq/llama3-8b-8192", "gemini/gemini-1.5-flash", "cohere/command-r"],
    messages=[{"role": "user", "content": "Tell me a story about a robot."}]
)
```

### Error Handling

LiteLLM standardizes errors across all providers. You can catch specific errors like `RateLimitError` or `APIConnectionError`.

```python
import litellm

try:
    response = await litellm.acompletion(
        models=["groq/llama3-8b-8192", "gemini/gemini-1.5-flash"],
        messages=[{"role": "user", "content": "..."}]
    )
except litellm.RateLimitError as e:
    print(f"Rate limit error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
```
