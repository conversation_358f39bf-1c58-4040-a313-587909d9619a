# Provider free-tier rate limits & handling (summary)

This document summarizes free-tier/quota behaviours and recommended handling strategies for the providers we've researched: Groq, Google Gemini, Cohere, and OpenRouter. Use it as a quick reference when adding provider drivers and rate-limit logic in `ai_manager.py`.

## Important notes & caveats
- Provider limits vary by model, account, and billing tier — always verify the exact quotas in the provider dashboard/console for the API key you plan to use.
- Most providers return HTTP 429 for rate-limit breaches. Many also expose helpful headers such as `Retry-After` and `x-ratelimit-*` — parse and honor them when present.
- Distinguish between request-count limits (requests/minute) and token-based limits (tokens/minute). Token limits require tracking tokens consumed per request.

---

## OpenRouter
- Source: OpenRouter docs (`/docs/limits` and FAQ).
- Free-model specifics (from docs):
  - Free model variants (model IDs ending with `:free`) are intentionally rate-limited.
  - Free-model request limit: up to **20 requests per minute**.
  - Per-day limits for free-model requests:
    - If you have purchased < 10 credits: **50 free-model requests/day**.
    - If you have purchased >= 10 credits: **1000 free-model requests/day**.
  - OpenRouter may also return 402 for negative credit balances; they enforce global capacity and model-specific limits.
- Behavior & headers: OpenRouter exposes an API to check key usage (`GET /api/v1/key`) which returns `usage`, `limit`, and `is_free_tier`. Respect returned `limit` and `usage` when available.

### Recommended handling
- If you receive 429: check `Retry-After` header; if present, respect it. If not present, back off using exponential backoff starting at 2s up to 300s.
- For free-models, limit requests to 20/min per key by default, and enforce daily caps (50 or 1000/day) depending on credit state.

---

## Groq
- Source: Groq docs and model pages (rate limits are model-dependent).
- Groq measures limits in multiple dimensions: RPM (requests/minute), TPM (tokens/minute), RPD/TPD (requests/tokens per day). Limits are generally model-specific and shown in the Groq dashboard.
- On limit exceed: Groq returns `429 Too Many Requests`. Response headers often include `Retry-After` and `x-ratelimit-*` style fields.

### Recommended handling
- Always sanitize and send only the fields the Groq API supports (e.g., `role` + `content` for messages) to avoid 400s.
- On 429: parse `Retry-After` (seconds) and `x-ratelimit-reset`/`x-ratelimit-remaining` headers when present and set provider cooldowns accordingly.
- Track token usage (TPM) per model when possible — Groq shows TPM quotas; conservative defaults: avoid sending very long contexts when nearing quotas.

---

## Google Gemini (Generative Language API)
- Source: Google Cloud / Gemini docs (quotas vary by model and tier).
- Quotas are attached to your Google Cloud project and may differ by model & billing tier. Free-tier / experiment quotas are typically conservative and measured in RPM / TPM / daily quotas.
- On exceed: API returns 429 (RESOURCE_EXHAUSTED) with relevant error JSON and sometimes `Retry-After`.

### Recommended handling
- Query the Google Cloud console for exact per-project quotas for the model you plan to use (the console lists per-minute and per-day quotas).
- Honor `Retry-After` and consider token accounting for large prompts/completions. Use conservative defaults (e.g., treat repeated 429s as requiring a 60–300s cooldown for that provider/model).

---

## Cohere
- Source: Cohere docs and community summaries.
- Trial / free API keys are intentionally limited. Example community/FAQ mentions: trial keys often have modest monthly totals (on the order of ~1k calls/month) and endpoint-specific per-minute limits (chat endpoints may be around low-double-digit requests/min for trials).
- On exceed: Cohere returns 429 and may include headers describing limits.

### Recommended handling
- Treat Cohere trial keys as low-capacity: implement strict per-key request throttling (e.g., 10–20 RPM) unless you confirm a higher quota in the Cohere dashboard.
- Parse `Retry-After` and any `x-ratelimit-*` headers; implement token accounting if you use large prompts.

---

## Generic handling guidelines
- Detection: treat HTTP 429 as rate-limit; also pay attention to 402 (payment/credits) and provider-specific JSON messages that indicate quota exhaustion.
- Header parsing: prefer `Retry-After` (seconds) first; fall back to `x-ratelimit-reset`, `x-ratelimit-remaining`, or `x-ratelimit-limit` where provided.
- Cooldown strategy:
  - If `Retry-After` present: set cooldown for that provider to that many seconds.
  - Else on first 429: conservative cooldown of 60s; on repeated 429s within a short window escalate to 300s.
  - Optionally apply token-based cooldowns when `x-ratelimit-remaining-tokens` is low.
- Provider fallbacks: mark a provider temporarily unavailable when it returns 429 or repeated 5xx errors; prefer next provider in priority list and record the cooldown in DB.
- Persistent storage: persist cooldowns across restarts (we added `rate_limit_cooldowns` table for that purpose).

## Quick checklist for implementing provider drivers
1. Validate & sanitize outbound payloads to match provider schema (avoid extraneous metadata).
2. Parse `Retry-After` and `x-ratelimit-*` headers; convert to seconds and store as cooldown.
3. If provider returns 429 or quota error JSON, set provider cooldown and fall back.
4. Track tokens used when provider uses token-based quotas; expose usage via admin introspection endpoints.

---

If you want, I can convert these recommendations into a short provider driver template and a shared helper (parse headers, set cooldown, token accounting) for `providers/` and `ai_manager.py`.
