import logging
from typing import Dict, List
import litellm

log = logging.getLogger(__name__)

# Model priority list. LiteLLM will try them in this order.
# Add the models you have API keys for in your .env file.
MODELS = [
    "gemini/gemini-2.5-flash",
    "openrouter/openai/gpt-oss-120b",
    "groq/llama4-maverick-17b-128e-instruct",
    "command-a",
    "cerebras/qwen-3-235b-a22b",
    "together_ai/meta-llama/Llama-3.3-70B-Instruct-hf",
]


async def get_ai_response(conversation_history: List[Dict]) -> str:
    """
    Gets an AI response from LiteLLM, attempting models in the priority list.
    Now handles a rich conversation history object.
    """
    system_prompt = "You are <PERSON><PERSON>, a helpful AI assistant on Discord. Be conversational and friendly."

    formatted_messages: List[Dict] = [{"role": "system", "content": system_prompt}]

    for message in conversation_history:
        if message["role"] == "user":
            author = message["author_details"]
            # Format the user's message with their identity details
            content = (
                f"User '{author['username']}' (Display Name: '{author['display_name']}', "
                f"Nickname: '{author['server_nickname']}') says: {message['content']}"
            )
            formatted_messages.append({"role": "user", "content": content})
        else:  # role == 'assistant'
            formatted_messages.append(
                {"role": "assistant", "content": message["content"]}
            )

    try:
        log.info(f"Attempting completion with models: {MODELS}")
        response = await litellm.acompletion(
            model=MODELS[0],
            fallbacks=MODELS[1:],
            messages=formatted_messages,
        )
        # LiteLLM returns a ModelResponse object, get the content
        response_content = response.choices[0].message.content
        log.info("LiteLLM completion successful.")
        return response_content
    except litellm.RateLimitError as e:
        log.error(f"LiteLLM Rate Limit Error: {e}")
        return "I'm a bit overwhelmed with requests right now. Please try again in a moment."
    except Exception as e:
        log.error(f"An unexpected error occurred with LiteLLM: {e}", exc_info=True)
        return "I'm having trouble connecting to my thinking-cores. Please try again later."
