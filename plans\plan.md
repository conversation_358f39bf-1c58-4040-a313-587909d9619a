## **Master Document: Yuna AI Discord Bot**

### **Version 2.0**

### **1. Project Overview**

**Yuna** is a modular, multi-provider AI chatbot for Discord designed for resilience and adaptability. Its core purpose is to provide a continuous and high-quality conversational AI experience by intelligently navigating the strict rate limits of various free-tier AI API services.

The architecture is built around **LiteLLM**, a powerful library that provides a unified interface for over 100 LLM APIs. This choice eliminates the need to write and maintain custom code for each provider, dramatically simplifying the codebase and making the bot easier to expand and maintain.

### **2. Core Features**

*   **Unified AI Integration:** Powered by LiteLLM, allowing seamless access to dozens of AI providers with a single, consistent interface.
*   **Automatic Provider Fallback:** Natively handles API failures and rate limits. If a request to a primary model fails, it automatically retries with the next model in a prioritized list.
*   **Simplified Configuration:** API keys are managed as standard environment variables, which LiteLLM automatically detects and uses.
*   **Rich Contextual Conversations:** Understands conversation history based on the method of interaction:
    *   **Direct Mentions:** Responds to single, direct questions.
    *   **Reply Chains:** Follows the entire thread of a conversation for deep context.
    *   **Dedicated AI Channels:** Treats the channel as an ongoing chat room, using recent message history for context.
*   **Enhanced User Context:** Enriches the AI's prompt with details about the message author (username, display name, server nickname).
*   **Server-Specific Configuration:** Server administrators can designate specific "AI Channels" where Yuna will respond to every message.

### **3. System Architecture**

The architecture is now significantly streamlined. The complex, multi-file provider layer is replaced entirely by the LiteLLM library.

```
+---------------------------+       +-------------------------+       +------------------------+
|   Discord Bot Front-End   |       |  AI Service Manager     |       |   LiteLLM Library      |
|      (yuna_bot.py)        |------>|    (ai_manager.py)      |------>|------------------------|
|---------------------------|       |-------------------------|       | - Unified `acompletion`|       +--> (Groq API)
| - Event Handling (on_message) |       | - Context Formatting    |       | - API Key Management   |       |
| - Command Handling (/set_ai)  |       | - Calls LiteLLM         |       | - Fallback Logic       |------>+--> (Gemini API)
| - Context Gathering Logic |<------| - Standardized Errors   |       | - Error Normalization  |       |
| - User-Facing Responses   |       +-------------------------+       +------------------------+       +--> (Cohere API)
+---------------------------+                                                                        |
            |                                                                                        +--> (etc...)
            |
            v
  +-------------------------+
  |    Database Interface   |
  |      (database.py)      |
  |-------------------------|
  | - AI Channel Configs    |
  +-------------------------+
```

#### **Component Responsibilities:**

*   **Discord Bot Front-End (`yuna_bot.py`):** Unchanged. This remains the user's point of interaction. It handles Discord events, gathers context, and formats the final response.
*   **AI Service Manager (`ai_manager.py`):** **Vastly simplified.** Its role is now to:
    1.  Receive the structured conversation history from the front-end.
    2.  Format the history into the standard `messages` list.
    3.  Make a **single, asynchronous call** to `litellm.acompletion`, providing a list of models to try.
    4.  Catch standardized LiteLLM exceptions (`RateLimitError`, `APIConnectionError`).
*   **`providers/` Directory:** **Eliminated.** This entire layer of custom code is no longer necessary.
*   **Database Interface (`database.py`):** **Simplified.** The database is now only needed for storing application state (like AI channels), not for managing API provider state like cooldowns.

### **4. Database Schema (Simplified)**

The database is leaner, as LiteLLM's fallback mechanism removes the need for a manual cooldown system.

| **Table: `ai_channels`** | |
| :--- | :--- |
| `channel_id` | INTEGER PRIMARY KEY |
| `guild_id` | INTEGER NOT NULL |

*Note: API keys are now managed exclusively in the `.env` file, adhering to best practices.*

### **5. Technology Stack**

*   **Language:** Python 3.10+
*   **Core Library:** **`litellm`** (for all AI interactions)
*   **Discord API Wrapper:** `discord.py`
*   **HTTP Requests:** `aiohttp` (used internally by `discord.py` and `litellm`)
*   **Database:** `sqlite3`
*   **Configuration:** `python-dotenv`

### **6. Revised Implementation Plan**

The development process is now faster and more focused on features rather than infrastructure.

#### **Phase 1: Foundation & The First LiteLLM Conversation**

*Goal: Get a minimally viable bot running that can respond using a single model via LiteLLM.*

1.  **Project Setup:** Initialize the project, create the virtual environment, and install libraries: `pip install discord.py python-dotenv litellm`.
2.  **Environment Setup:** Create a `.env` file and add your `DISCORD_TOKEN` and the API key for your first provider (e.g., `GROQ_API_KEY=gsk_...`).
3.  **Basic Bot (`yuna_bot.py`):** Implement the standard `on_ready` event and a basic `/chat` slash command.
4.  **Simple AI Manager (`ai_manager.py`):** Create the `get_ai_response` function. It will make a simple call to `litellm.acompletion` with a single hardcoded model (e.g., `model="groq/llama3-8b-8192"`).
5.  **Integration:** Connect the `/chat` command to the AI manager. At the end of this phase, the bot should be online and responding to a basic command.

#### **Phase 2: Advanced Interaction & Contextual Awareness**

*Goal: Implement the sophisticated logic that makes Yuna feel intelligent and conversational. **This phase is identical to the original plan**, as it primarily concerns the Discord front-end.*

1.  **Database for AI Channels:** Implement the simplified `database.py` with the `ai_channels` table and its management functions.
2.  **Admin Commands:** Create `/set_ai_channel` and `/remove_ai_channel` slash commands.
3.  **`on_message` Handler:** Implement the main event listener to detect mentions, replies, and messages in AI channels.
4.  **Context Gathering Logic:** Create the `gather_context` helper function to build the conversation history object, including `author_details`.
5.  **AI Manager Upgrade:** Ensure the `ai_manager.py` correctly formats the rich `conversation_history` object before passing it to LiteLLM.

#### **Phase 3: Activating Resilience & Expansion**

*Goal: Leverage LiteLLM's full power to make the bot resilient and multi-provider. This phase is now incredibly fast.*

1.  **Add More API Keys:** Update your `.env` file with keys for your fallback providers (e.g., `GEMINI_API_KEY`, `COHERE_API_KEY`).
2.  **Activate Fallbacks in AI Manager:** In `ai_manager.py`, modify the `litellm.acompletion` call. Replace the `model="..."` parameter with a `models=[...]` list, ordered by priority.
    *   **Example:** `models=["groq/llama3-8b-8192", "gemini/gemini-1.5-flash", "cohere/command-r"]`
3.  **Enhance Error Handling:** Implement `try...except` blocks for standardized LiteLLM errors like `litellm.RateLimitError` to provide better user feedback.
4.  **Test Fallbacks:** Test the system by temporarily using an invalid API key for your primary provider and verifying that the bot successfully responds using the second provider in the list.

#### **Phase 4: Refinement, Logging & Deployment**

*Goal: Polish the application and prepare it for continuous operation. **This phase is also identical to the original plan.***

1.  **Implement Structured Logging:** Replace `print()` statements with a proper `logging` configuration to capture events, AI provider choices, and errors to both the console and a file.
2.  **Code Cleanup:** Refactor, add docstrings, comments, and type hints to ensure the code is clean and maintainable.
3.  **Deployment Preparation:** Create a `requirements.txt` file (`pip freeze > requirements.txt`) and a comprehensive `README.md` explaining setup, configuration, and usage.
4.  **Deployment:** Host the bot on a VPS or cloud service for 24/7 operation.