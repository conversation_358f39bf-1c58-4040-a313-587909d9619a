# Phase 2: Advanced Interaction & Contextual Awareness

**Goal:** Evolve <PERSON><PERSON> from a simple command-based bot into a truly interactive and conversational agent. This phase implements the core logic for understanding mentions, replies, and dedicated chat channels, enriching the AI's context with user information.

---

### **Key Objectives**

1.  **Implement `on_message` Logic:** Make the bot listen to all messages to detect triggers.
2.  **Create Context Gathering Function:** Build a sophisticated function to assemble conversation history based on interaction type.
3.  **Integrate User Details:** Add author username, display name, and nickname to the context sent to the AI.
4.  **Configure AI Channels:** Allow server admins to designate channels where <PERSON><PERSON> will be fully active.
5.  **Upgrade AI Manager:** Modify the manager to handle the new, rich context object.

### **Step-by-Step Implementation Plan**

#### **Step 1: Database Migration (`database.py`)**

We need to add the `ai_channels` table. Add this to your `initialize_database` function and create new functions to manage these channels.

```python
# database.py additions

def initialize_database():
    # ... (existing code for api_keys table) ...
    # Add the new table
    cur.execute('''
        CREATE TABLE IF NOT EXISTS ai_channels (
            channel_id INTEGER PRIMARY KEY,
            guild_id INTEGER NOT NULL
        )
    ''')
    con.commit()
    con.close()
    print("Database initialized.")

def add_ai_channel(channel_id, guild_id):
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("INSERT OR REPLACE INTO ai_channels (channel_id, guild_id) VALUES (?, ?)", (channel_id, guild_id))
    con.commit()
    con.close()

def remove_ai_channel(channel_id):
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("DELETE FROM ai_channels WHERE channel_id = ?", (channel_id,))
    con.commit()
    con.close()

def is_ai_channel(channel_id):
    con = sqlite3.connect(DB_NAME)
    cur = con.cursor()
    cur.execute("SELECT 1 FROM ai_channels WHERE channel_id = ?", (channel_id,))
    result = cur.fetchone()
    con.close()
    return result is not None
```

#### **Step 2: AI Manager Upgrade (`ai_manager.py`)**

The manager must now format the rich context object into a string that the AI model can understand.

```python
# ai_manager.py modifications

async def get_ai_response(conversation_history):
    """
    The main entry point for getting an AI response.
    Now handles a rich conversation history object.
    """
    groq_api_key = database.get_api_key("groq")
    if not groq_api_key:
        return "Error: Groq API key is not configured."

    # This is the new, crucial part: Formatting the context for the model.
    # We will create a system prompt and format the user messages.
    system_prompt = "You are Yuna, a helpful AI assistant on Discord. Be conversational and friendly."
    
    formatted_messages = [{"role": "system", "content": system_prompt}]
    
    for message in conversation_history:
        if message['role'] == 'user':
            author = message['author_details']
            # Format the user's message with their identity details
            content = (
                f"User '{author['username']}' (Display Name: '{author['display_name']}', "
                f"Nickname: '{author['server_nickname']}') says: {message['content']}"
            )
            formatted_messages.append({"role": "user", "content": content})
        else: # role == 'assistant'
            formatted_messages.append({"role": "assistant", "content": message['content']})

    try:
        # The provider module will now receive the fully formatted messages
        response = await groq.generate_from_messages(groq_api_key, formatted_messages)
        return response
    except Exception as e:
        print(f"Error calling AI provider: {e}")
        return "Sorry, I encountered an error while trying to think."
```

#### **Step 3: Provider Module Update (`providers/groq.py`)**

The provider module needs a new function that accepts the pre-formatted message list directly.

```python
# providers/groq.py modifications

# The old `generate` function can be removed or kept for testing.
# Let's create a new, more direct function.

async def generate_from_messages(api_key, formatted_messages):
    """
    Generates a response from a pre-formatted list of message dictionaries.
    """
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3-8b-8192",
        "messages": formatted_messages
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(API_URL, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return data['choices'][0]['message']['content']
            else:
                error_text = await response.text()
                raise Exception(f"Groq API Error: {response.status} - {error_text}")
```

#### **Step 4: The Core Logic in `yuna_bot.py`**

This is the largest change. We add the admin commands and the `on_message` event handler with its context-gathering helper.

```python
# yuna_bot.py modifications and additions

# ... (imports and bot setup) ...

# --- Helper function for context gathering ---
async def gather_context(message, bot):
    """Analyzes a message and returns a formatted conversation history."""
    history = []
    
    # Helper to create the author details dictionary
    def get_author_details(author):
        return {
            "username": author.name,
            "display_name": author.display_name,
            "server_nickname": author.nick or author.display_name
        }

    # Case C: AI Channel
    if database.is_ai_channel(message.channel.id):
        # Fetch last 15 messages, reverse them for chronological order
        channel_messages = [msg async for msg in message.channel.history(limit=15)]
        channel_messages.reverse()
        for msg in channel_messages:
            role = 'assistant' if msg.author == bot.user else 'user'
            if msg.content:
                entry = {'role': role, 'content': msg.content}
                if role == 'user':
                    entry['author_details'] = get_author_details(msg.author)
                history.append(entry)
        return history

    # Determine if the bot should respond (mention or reply)
    is_mention = bot.user in message.mentions
    is_reply_to_bot = message.reference and message.reference.resolved and message.reference.resolved.author == bot.user
    
    if not (is_mention or is_reply_to_bot):
        return None # Bot should not respond

    # Case B: Reply Chain
    if is_reply_to_bot:
        current_message = message
        # Walk up the reply chain
        for _ in range(15): # Limit to 15 replies
            role = 'assistant' if current_message.author == bot.user else 'user'
            if current_message.content:
                entry = {'role': role, 'content': current_message.content}
                if role == 'user':
                    entry['author_details'] = get_author_details(current_message.author)
                history.insert(0, entry) # Prepend to keep chronological order

            if not current_message.reference or not current_message.reference.resolved:
                break
            
            current_message = await message.channel.fetch_message(current_message.reference.message_id)
        
        return history

    # Case A: Direct Mention
    if is_mention:
        prompt = message.content.replace(f'<@{bot.user.id}>', '').strip()
        if prompt:
            history.append({
                'role': 'user', 
                'content': prompt,
                'author_details': get_author_details(message.author)
            })
        return history
    
    return None

# --- New Admin Commands ---
@bot.tree.command(name="set_ai_channel", description="Sets the current channel as an AI chat channel.")
@discord.app_commands.checks.has_permissions(administrator=True)
async def set_ai_channel(interaction: discord.Interaction):
    channel_id = interaction.channel_id
    guild_id = interaction.guild_id
    database.add_ai_channel(channel_id, guild_id)
    await interaction.response.send_message(f"Channel <#{channel_id}> is now an AI channel.", ephemeral=True)

@bot.tree.command(name="remove_ai_channel", description="Removes the current channel from the AI chat list.")
@discord.app_commands.checks.has_permissions(administrator=True)
async def remove_ai_channel(interaction: discord.Interaction):
    channel_id = interaction.channel_id
    database.remove_ai_channel(channel_id)
    await interaction.response.send_message(f"Channel <#{channel_id}> is no longer an AI channel.", ephemeral=True)


# --- The main event listener ---
@bot.event
async def on_message(message):
    if message.author == bot.user:
        return

    conversation_history = await gather_context(message, bot)

    if conversation_history:
        async with message.channel.typing():
            try:
                response_text = await ai_manager.get_ai_response(conversation_history)
                # Reply to the user's message to maintain the thread
                await message.reply(response_text)
            except Exception as e:
                print(f"Error getting AI response: {e}")
                await message.reply("Sorry, I encountered an error while thinking.")
    
    # We remove process_commands if we are moving away from prefix commands
    # await bot.process_commands(message)
```

### **Definition of Done for Phase 2**

-   [ ] The database is updated with the `ai_channels` table.
-   [ ] The `/set_ai_channel` and `/remove_ai_channel` commands work for server admins.
-   [ ] The bot does **not** respond to normal messages in non-AI channels.
-   [ ] The bot **does** respond when directly mentioned (`@Yuna ...`).
-   [ ] The bot **does** respond when a user replies to one of its messages.
-   [ ] The bot responds to **every** message in a designated AI channel.
-   [ ] The AI's responses seem to understand the context from previous messages in replies and AI channels.